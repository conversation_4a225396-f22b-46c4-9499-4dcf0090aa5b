:root {
  --primary-color: #0d6efd;
  --sidebar-bg: #0c4a7a;
  --sidebar-hover: #0a3d66;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
}

/* Sidebar */
.sidebar {
  background-color: var(--sidebar-bg);
  min-height: 100vh;
  position: sticky;
  top: 0;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.nav-menu .nav-link {
  color: rgba(255, 255, 255, 0.8);
  padding: 0.8rem 1.5rem;
  border-left: 3px solid transparent;
}

.nav-menu .nav-link:hover {
  color: #fff;
  background-color: var(--sidebar-hover);
}

.nav-menu .nav-link.active {
  color: #fff;
  border-left: 3px solid #fff;
  background-color: var(--sidebar-hover);
}

.dropdown-toggle::after {
  float: right;
  margin-top: 8px;
}

/* Main content */
.main-content {
  background-color: #f8f9fa;
}

/* Cards */
.card {
  border-radius: 10px;
  transition: transform 0.2s;
}

.card:hover {
  transform: translateY(-5px);
}

.icon-bg {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Tables */
.table th {
  font-weight: 600;
  color: #495057;
}

.table td {
  vertical-align: middle;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .sidebar {
    position: fixed;
    z-index: 999;
    width: 250px;
    transform: translateX(-100%);
    transition: transform 0.3s;
  }
  
  .sidebar.show {
    transform: translateX(0);
  }
  
  .main-content {
    width: 100%;
  }
}