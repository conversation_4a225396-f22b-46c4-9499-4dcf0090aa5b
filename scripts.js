// Initialize charts when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  // Application Status Chart
  const applicationCtx = document.getElementById('applicationChart').getContext('2d');
  const applicationChart = new Chart(applicationCtx, {
    type: 'bar',
    data: {
      labels: ['CFO', 'CTO', 'Panchayat', 'Electrical', 'Other'],
      datasets: [
        {
          label: 'Approved',
          data: [4, 5, 0, 0, 2],
          backgroundColor: '#0d6efd',
          borderRadius: 5
        },
        {
          label: 'Rejected',
          data: [1, 0, 1, 0, 0],
          backgroundColor: '#fd7e14',
          borderRadius: 5
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          grid: {
            drawBorder: false
          }
        },
        x: {
          grid: {
            display: false
          }
        }
      }
    }
  });

  // Status Pie Chart
  const statusCtx = document.getElementById('statusPieChart').getContext('2d');
  const statusPieChart = new Chart(statusCtx, {
    type: 'doughnut',
    data: {
      labels: ['Approved', 'Pending', 'Rejected'],
      datasets: [{
        data: [86, 28, 10],
        backgroundColor: ['#198754', '#ffc107', '#dc3545'],
        borderWidth: 0
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom'
        }
      },
      cutout: '70%'
    }
  });

  // Toggle sidebar on mobile
  const toggleSidebar = document.querySelector('.toggle-sidebar');
  if (toggleSidebar) {
    toggleSidebar.addEventListener('click', function() {
      document.querySelector('.sidebar').classList.toggle('show');
    });
  }
});